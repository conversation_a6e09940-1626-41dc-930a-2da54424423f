# NEXTNOVEL APP 部署指南

## 部署架构概览

### 基础设施组件
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile Apps   │    │   Web Client    │    │  Admin Panel    │
│  (iOS/Android)  │    │    (React)      │    │    (React)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Load Balancer  │
                    │    (Nginx)      │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │    │  WebSocket      │    │  Static Assets  │
│   (Node.js)     │    │   Server        │    │     (CDN)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Microservices │
                    │     Cluster     │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │     Redis       │    │   File Storage  │
│   (Primary)     │    │    (Cache)      │    │     (S3)        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 环境配置

### 生产环境 (Production)
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  # 负载均衡器
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - api
      - websocket
    restart: unless-stopped

  # API服务
  api:
    build:
      context: .
      dockerfile: Dockerfile.api
    environment:
      NODE_ENV: production
      DATABASE_URL: ${DATABASE_URL}
      REDIS_URL: ${REDIS_URL}
      JWT_SECRET: ${JWT_SECRET}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    deploy:
      replicas: 3

  # WebSocket服务
  websocket:
    build:
      context: .
      dockerfile: Dockerfile.websocket
    environment:
      NODE_ENV: production
      REDIS_URL: ${REDIS_URL}
    depends_on:
      - redis
    restart: unless-stopped
    deploy:
      replicas: 2

  # 后台任务处理器
  worker:
    build:
      context: .
      dockerfile: Dockerfile.worker
    environment:
      NODE_ENV: production
      DATABASE_URL: ${DATABASE_URL}
      REDIS_URL: ${REDIS_URL}
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    deploy:
      replicas: 2

  # 数据库
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    restart: unless-stopped

  # 缓存
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # 监控
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
    restart: unless-stopped

  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  grafana_data:
```

### Nginx配置
```nginx
# nginx/nginx.conf
upstream api_backend {
    least_conn;
    server api:3000 max_fails=3 fail_timeout=30s;
}

upstream websocket_backend {
    ip_hash;
    server websocket:3001 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    server_name nextnovel.app www.nextnovel.app;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name nextnovel.app www.nextnovel.app;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # API路由
    location /api/ {
        proxy_pass http://api_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓存设置
        proxy_cache_bypass $http_upgrade;
        proxy_no_cache $http_upgrade;
    }

    # WebSocket路由
    location /ws/ {
        proxy_pass http://websocket_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket特定设置
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
    }

    # 静态文件
    location /static/ {
        alias /var/www/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        gzip_static on;
    }

    # Web应用
    location / {
        root /var/www/html;
        try_files $uri $uri/ /index.html;
        
        # PWA支持
        location /manifest.json {
            add_header Cache-Control "no-cache";
        }
        
        location /service-worker.js {
            add_header Cache-Control "no-cache";
        }
    }
}
```

## 移动应用部署

### iOS部署 (App Store)
```bash
# 1. 构建生产版本
expo build:ios --release-channel production

# 2. 使用EAS Build (推荐)
npm install -g @expo/eas-cli
eas login
eas build:configure
eas build --platform ios --profile production

# 3. 提交到App Store
eas submit --platform ios --profile production
```

#### iOS配置文件
```json
// eas.json
{
  "cli": {
    "version": ">= 3.0.0"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal",
      "ios": {
        "resourceClass": "m1-medium"
      }
    },
    "preview": {
      "distribution": "internal",
      "ios": {
        "simulator": true,
        "resourceClass": "m1-medium"
      }
    },
    "production": {
      "ios": {
        "resourceClass": "m1-medium",
        "bundleIdentifier": "com.nextnovel.app"
      },
      "android": {
        "resourceClass": "medium",
        "buildType": "apk"
      }
    }
  },
  "submit": {
    "production": {
      "ios": {
        "appleId": "<EMAIL>",
        "ascAppId": "**********",
        "appleTeamId": "ABCDEFGHIJ"
      },
      "android": {
        "serviceAccountKeyPath": "./google-service-account.json",
        "track": "production"
      }
    }
  }
}
```

### Android部署 (Google Play)
```bash
# 1. 构建生产版本
expo build:android --release-channel production

# 2. 使用EAS Build
eas build --platform android --profile production

# 3. 提交到Google Play
eas submit --platform android --profile production
```

#### Android签名配置
```bash
# 生成签名密钥
keytool -genkeypair -v -keystore nextnovel-release-key.keystore \
  -alias nextnovel-key-alias -keyalg RSA -keysize 2048 -validity 10000

# 配置gradle.properties
NEXTNOVEL_UPLOAD_STORE_FILE=nextnovel-release-key.keystore
NEXTNOVEL_UPLOAD_KEY_ALIAS=nextnovel-key-alias
NEXTNOVEL_UPLOAD_STORE_PASSWORD=your_store_password
NEXTNOVEL_UPLOAD_KEY_PASSWORD=your_key_password
```

## CI/CD流水线

### GitHub Actions配置
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm test
      
      - name: Run linting
        run: npm run lint
      
      - name: Type checking
        run: npm run type-check

  build-mobile:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Expo
        uses: expo/expo-github-action@v8
        with:
          expo-version: latest
          token: ${{ secrets.EXPO_TOKEN }}
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build iOS
        run: eas build --platform ios --profile production --non-interactive
      
      - name: Build Android
        run: eas build --platform android --profile production --non-interactive

  deploy-backend:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      
      - name: Deploy to production
        uses: appleboy/ssh-action@v0.1.5
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            cd /opt/nextnovel
            git pull origin main
            docker-compose -f docker-compose.prod.yml down
            docker-compose -f docker-compose.prod.yml up -d --build
            docker system prune -f

  deploy-web:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build web app
        run: npm run build:web
        env:
          REACT_APP_API_URL: ${{ secrets.API_URL }}
      
      - name: Deploy to S3
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
      
      - name: Sync to S3
        run: aws s3 sync ./dist s3://${{ secrets.S3_BUCKET }} --delete
      
      - name: Invalidate CloudFront
        run: aws cloudfront create-invalidation --distribution-id ${{ secrets.CLOUDFRONT_ID }} --paths "/*"
```

## 监控和日志

### 应用监控
```typescript
// monitoring/metrics.ts
import { register, Counter, Histogram, Gauge } from 'prom-client';

// 业务指标
export const userRegistrations = new Counter({
  name: 'user_registrations_total',
  help: 'Total number of user registrations'
});

export const aiGenerations = new Counter({
  name: 'ai_generations_total',
  help: 'Total number of AI generations',
  labelNames: ['model', 'type']
});

export const responseTime = new Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status']
});

export const activeUsers = new Gauge({
  name: 'active_users',
  help: 'Number of currently active users'
});

// 导出所有指标
export const metricsRegistry = register;
```

### 日志聚合
```yaml
# logging/docker-compose.logging.yml
version: '3.8'

services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.5.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"

  logstash:
    image: docker.elastic.co/logstash/logstash:8.5.0
    volumes:
      - ./logstash/pipeline:/usr/share/logstash/pipeline
    ports:
      - "5044:5044"
    depends_on:
      - elasticsearch

  kibana:
    image: docker.elastic.co/kibana/kibana:8.5.0
    ports:
      - "5601:5601"
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    depends_on:
      - elasticsearch

  filebeat:
    image: docker.elastic.co/beats/filebeat:8.5.0
    volumes:
      - ./filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    depends_on:
      - logstash

volumes:
  elasticsearch_data:
```

## 备份和恢复

### 数据库备份
```bash
#!/bin/bash
# scripts/backup.sh

# 配置
DB_NAME="nextnovel"
DB_USER="postgres"
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/nextnovel_$DATE.sql"

# 创建备份
pg_dump -h localhost -U $DB_USER -d $DB_NAME > $BACKUP_FILE

# 压缩备份
gzip $BACKUP_FILE

# 上传到S3
aws s3 cp $BACKUP_FILE.gz s3://nextnovel-backups/database/

# 清理本地旧备份 (保留7天)
find $BACKUP_DIR -name "nextnovel_*.sql.gz" -mtime +7 -delete

echo "Backup completed: $BACKUP_FILE.gz"
```

### 自动备份配置
```bash
# 添加到crontab
# 每天凌晨2点执行备份
0 2 * * * /opt/nextnovel/scripts/backup.sh

# 每周日凌晨3点执行完整备份
0 3 * * 0 /opt/nextnovel/scripts/full-backup.sh
```

## 安全配置

### SSL/TLS配置
```bash
# 使用Let's Encrypt获取SSL证书
certbot --nginx -d nextnovel.app -d www.nextnovel.app

# 自动续期
echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -
```

### 防火墙配置
```bash
# UFW防火墙规则
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw enable
```

### 环境变量安全
```bash
# 使用Docker Secrets管理敏感信息
echo "your_jwt_secret" | docker secret create jwt_secret -
echo "your_db_password" | docker secret create db_password -

# 在docker-compose中引用
services:
  api:
    secrets:
      - jwt_secret
      - db_password
    environment:
      JWT_SECRET_FILE: /run/secrets/jwt_secret
      DB_PASSWORD_FILE: /run/secrets/db_password

secrets:
  jwt_secret:
    external: true
  db_password:
    external: true
```

通过以上部署指南，NEXTNOVEL APP可以安全、稳定地部署到生产环境，并具备完善的监控、备份和安全保障机制。
