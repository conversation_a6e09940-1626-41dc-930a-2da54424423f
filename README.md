# NEXTNOVEL APP 🎭📚

> 沉浸式AI互动小说应用 - 完全兼容SillyTavern生态系统

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![React Native](https://img.shields.io/badge/React%20Native-0.79.3-blue.svg)](https://reactnative.dev/)
[![Expo](https://img.shields.io/badge/Expo-SDK%2053-black.svg)](https://expo.dev/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.8.3-blue.svg)](https://www.typescriptlang.org/)

NEXTNOVEL是一款革命性的AI互动小说应用，采用"沉浸式体验优先"的设计理念，为用户提供角色扮演对话、互动小说阅读和世界观探索功能。项目完全兼容SillyTavern生态系统，支持角色卡、预设、正则表达式等内容的一键导入。

## ✨ 核心特性

### 🎭 沉浸式AI对话
- 🤖 支持多种AI模型 (OpenAI, Claude, 本地模型等)
- 🎨 完全兼容SillyTavern角色卡格式 (V1/V2)
- 👥 群聊和单聊模式
- 🎵 实时语音合成和识别
- 🖼️ AI生成角色头像和场景图

### 📚 互动小说阅读
- 🌳 分支剧情和动态叙事
- 🎨 AI生成插图和配乐
- 📖 个性化阅读体验
- ☁️ 进度同步和书签功能
- 📱 沉浸式阅读界面

### 🌍 世界观探索
- 🗺️ 交互式地图和时间线
- 📚 Lorebook知识库集成
- 🤝 协作式世界构建
- 🎭 角色扮演模式
- ⚡ 实时事件系统

### 🎨 AI辅助创作
- 🧭 引导式创作流程
- 👁️ 实时预览和测试
- 📦 模板和素材库
- 🔄 版本控制和协作
- 🤖 智能创作建议

### 🏛️ 社区生态
- 💬 Discord风格论坛
- 🔍 内容分享和发现
- 🏆 创作者激励机制
- 🎖️ 成就系统和徽章
- 📈 数据统计和分析

## 🚀 快速开始

### 环境要求
- Node.js 18.0.0+
- npm 9.0.0+
- Git 最新版本
- Android Studio (Android开发)
- Xcode (iOS开发，仅macOS)

### 安装步骤
```bash
# 1. 克隆项目
git clone https://github.com/your-org/nextnovel.git
cd nextnovel

# 2. 安装依赖
npm install

# 3. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填入必要的配置

# 4. 启动开发服务器
npm start

# 5. 在模拟器或设备上运行
npm run android  # Android
npm run ios      # iOS
npm run web      # Web浏览器
```

### 环境变量配置
```bash
# API配置
API_BASE_URL=http://localhost:3000/api
WEBSOCKET_URL=ws://localhost:3000

# AI服务配置
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
GOOGLE_AI_API_KEY=your_google_key

# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/nextnovel
REDIS_URL=redis://localhost:6379

# 文件存储
AWS_ACCESS_KEY_ID=your_aws_key
AWS_SECRET_ACCESS_KEY=your_aws_secret
AWS_S3_BUCKET=nextnovel-assets
```

## 📁 项目结构

```
nextnovel/
├── app/                    # Expo Router页面
│   ├── (tabs)/            # 底部导航页面
│   │   ├── home/          # 首页模块 (聊天/小说/世界)
│   │   ├── discovery/     # 发现页面
│   │   ├── creation/      # 创作工具
│   │   ├── forum/         # 社区论坛
│   │   └── profile/       # 个人中心
│   ├── modals/            # 模态页面
│   └── _layout.tsx        # 根布局
├── components/            # 可复用组件
│   ├── ui/               # 基础UI组件
│   ├── chat/             # 聊天相关组件
│   ├── novel/            # 小说阅读组件
│   ├── world/            # 世界观组件
│   └── creation/         # 创作工具组件
├── services/             # 业务服务层
│   ├── api/              # API服务
│   ├── storage/          # 数据存储
│   ├── sillytavern/      # SillyTavern兼容层
│   └── sync/             # 云同步服务
├── docs/                 # 项目文档
├── assets/               # 静态资源
└── utils/                # 工具函数
```

## 🛠️ 技术栈

### 前端技术
- **框架**: React Native 0.79.3 + Expo SDK 53
- **路由**: Expo Router 5.1.0 (文件系统路由)
- **状态管理**: React Context + useReducer
- **UI组件**: 自定义组件库 + React Native Elements
- **动画**: React Native Reanimated 3.17.4
- **手势**: React Native Gesture Handler 2.24.0
- **语言**: TypeScript 5.8.3

### 后端技术
- **运行时**: Node.js 18+
- **框架**: Express.js / Fastify
- **数据库**: PostgreSQL 15 + Redis 7
- **ORM**: Prisma / TypeORM
- **认证**: JWT + Refresh Token
- **文件存储**: AWS S3 / MinIO
- **实时通信**: WebSocket / Socket.io

### AI服务集成
- **OpenAI**: GPT-3.5/4, DALL-E 3
- **Anthropic**: Claude 3 (Haiku/Sonnet/Opus)
- **Google**: Gemini Pro/Ultra
- **本地模型**: Ollama, LM Studio, KoboldAI
- **语音服务**: Azure Speech, ElevenLabs, OpenAI TTS

## 📖 文档

详细的项目文档请查看 [docs](./docs/) 目录：

- 📋 [项目技术规范](./docs/PROJECT_SPECIFICATION.md) - 技术架构和功能设计
- 🔗 [SillyTavern生态集成](./docs/SILLYTAVERN_INTEGRATION.md) - 兼容性实现方案
- 🎨 [UI/UX设计规范](./docs/UI_UX_DESIGN_GUIDE.md) - 界面设计标准
- 🗄️ [API设计与数据库架构](./docs/API_DATABASE_DESIGN.md) - 后端架构设计
- 💻 [开发指南](./docs/DEVELOPMENT_GUIDE.md) - 开发环境和规范
- 🚀 [部署指南](./docs/DEPLOYMENT_GUIDE.md) - 生产环境部署

## 🗺️ 开发路线图

### Phase 1: 核心功能 (3个月)
- [x] 项目架构搭建
- [x] 基础UI组件库
- [ ] 聊天功能实现
- [ ] SillyTavern兼容层
- [ ] 角色卡导入导出

### Phase 2: 内容生态 (2个月)
- [ ] 发现页面和推荐算法
- [ ] 创作工具和AI辅助
- [ ] 社区论坛系统
- [ ] 内容分享机制

### Phase 3: 高级功能 (2个月)
- [ ] 世界观系统
- [ ] 互动小说引擎
- [ ] 语音合成和识别
- [ ] 实时协作功能

### Phase 4: 优化和扩展 (持续)
- [ ] 性能优化和监控
- [ ] 多语言支持
- [ ] 桌面版本开发
- [ ] 企业版功能

## 🤝 贡献指南

我们欢迎所有形式的贡献！请查看 [贡献指南](./CONTRIBUTING.md) 了解详细信息。

### 开发流程
1. Fork项目到个人仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

### 代码规范
- 遵循ESLint和Prettier配置
- 使用TypeScript严格模式
- 编写单元测试和文档
- 遵循Git提交信息规范

## 📄 许可证

本项目采用 [MIT License](./LICENSE) 开源协议。

## 📞 联系方式

- **项目主页**: https://github.com/your-org/nextnovel
- **官方网站**: https://nextnovel.app
- **技术支持**: <EMAIL>
- **商务合作**: <EMAIL>
- **Discord社区**: https://discord.gg/nextnovel

## 🙏 致谢

感谢以下开源项目和社区的支持：
- [Expo](https://expo.dev/) - 跨平台开发框架
- [React Native](https://reactnative.dev/) - 移动应用开发框架
- [SillyTavern](https://github.com/SillyTavern/SillyTavern) - AI聊天界面
- [OpenAI](https://openai.com/) - AI模型服务
- [Anthropic](https://anthropic.com/) - Claude AI模型

---

<div align="center">
  <strong>用AI重新定义互动小说体验</strong><br>
  Made with ❤️ by NextNovel Team
</div>
