# NEXTNOVEL APP 项目技术规范文档

## 项目概述

NEXTNOVEL是一款基于Expo/React Native的沉浸式AI互动小说应用，采用"沉浸式体验优先"的设计理念，为用户提供角色扮演对话、互动小说阅读和世界观探索功能。项目完全兼容SillyTavern生态系统，支持角色卡、预设、正则表达式等内容的一键导入。

## 技术栈

### 核心框架
- **前端框架**: React Native 0.79.3 + Expo SDK 53
- **路由系统**: Expo Router 5.1.0 (基于文件系统的路由)
- **状态管理**: React Context + useReducer (可扩展至Redux Toolkit)
- **UI组件库**: React Native Elements + 自定义组件
- **动画库**: React Native Reanimated 3.17.4
- **手势处理**: React Native Gesture Handler 2.24.0

### 开发工具
- **语言**: TypeScript 5.8.3
- **代码规范**: ESLint + Expo配置
- **包管理**: npm
- **构建工具**: Metro Bundler
- **版本控制**: Git

### 后端服务
- **AI服务**: 支持多种AI API (OpenAI, Claude, 本地模型等)
- **数据存储**: SQLite (本地) + Cloud Storage (同步)
- **文件管理**: Expo FileSystem
- **网络请求**: Fetch API + 自定义封装

## 项目架构

### 目录结构
```
nextnovel/
├── app/                    # Expo Router页面
│   ├── (tabs)/            # 底部导航页面
│   │   ├── home/          # 首页模块
│   │   ├── discovery/     # 发现页面
│   │   ├── creation/      # 创作页面
│   │   ├── forum/         # 论坛页面
│   │   └── profile/       # 个人页面
│   ├── _layout.tsx        # 根布局
│   └── index.tsx          # 入口页面
├── components/            # 可复用组件
│   ├── ui/               # 基础UI组件
│   ├── chat/             # 聊天相关组件
│   ├── novel/            # 小说阅读组件
│   ├── world/            # 世界观组件
│   └── creation/         # 创作工具组件
├── services/             # 业务服务层
│   ├── ai/               # AI服务集成
│   ├── storage/          # 数据存储
│   ├── sillytavern/      # SillyTavern兼容层
│   └── sync/             # 云同步服务
├── types/                # TypeScript类型定义
├── utils/                # 工具函数
├── constants/            # 常量配置
├── hooks/                # 自定义Hooks
└── assets/               # 静态资源
```

## 核心功能模块

### 1. 首页模块 (Home)
#### 1.1 聊天功能 (Chat)
**A. 角色库 (Character Library)**
- 响应式网格布局的角色卡片展示
- 搜索、筛选、排序功能
- 群聊创建和管理
- 角色卡片快捷操作菜单

**B. AI对话交互界面**
- 全屏沉浸式对话体验
- 实时打字效果和语音合成
- 消息历史管理和搜索
- 多模态输入支持(文本、语音、图片)

#### 1.2 小说功能 (Novel)
**A. 小说库 (Novel Library)**
- 数字书架式界面设计
- 阅读进度跟踪和同步
- 分类管理和个性化推荐

**B. 阅读/互动界面**
- 自适应排版和主题切换
- 分支选择和动态叙事
- AI生成插图集成
- 书签和笔记功能

#### 1.3 世界观功能 (World)
**A. 世界库 (World Library)**
- 世界卡片管理和预览
- 标签分类和搜索过滤

**B. 世界探索界面**
- 交互式地图导航
- 时间线和事件系统
- Lorebook集成查看
- 角色扮演模式切换

### 2. 发现页面 (Discovery)
- 内容推荐算法
- 创作者专题展示
- 实时热门榜单
- 交互式内容预览
- 一键体验和下载

### 3. 创作页面 (Creation)
- AI辅助创作工具
- 引导式vs专家模式
- 实时预览和测试
- 模板和素材库
- 版本控制和协作

### 4. 论坛页面 (Forum)
- Discord风格的社区系统
- 富内容分享和嵌入
- 实时消息和通知
- 角色扮演频道支持

### 5. 个人页面 (Profile)
- 个人作品展示
- 成就系统和徽章
- 使用统计和分析
- 设置和偏好管理

## SillyTavern生态集成

### 兼容性支持
1. **角色卡格式**
   - Character Card V1/V2格式完全支持
   - PNG嵌入式元数据解析
   - JSON格式角色卡导入/导出

2. **预设系统**
   - Prompt模板兼容
   - 系统消息和用户消息格式
   - 参数配置(temperature, top_p等)

3. **正则表达式**
   - 输入/输出文本处理规则
   - 自定义替换和格式化
   - 条件触发和动态响应

4. **世界信息 (World Info/Lorebook)**
   - 关键词触发机制
   - 上下文注入策略
   - 优先级和权重系统

### 导入/导出功能
```typescript
// 角色卡导入示例
interface CharacterCard {
  name: string;
  description: string;
  personality: string;
  scenario: string;
  first_mes: string;
  mes_example: string;
  avatar?: string;
  // SillyTavern扩展字段
  extensions?: {
    depth_prompt?: string;
    regex?: RegexRule[];
    world_info?: WorldInfoEntry[];
  };
}

// 一键导入功能
const importCharacterCard = async (file: File) => {
  const cardData = await parseCharacterCard(file);
  const character = await createCharacterFromCard(cardData);
  return character;
};
```

### API集成层
```typescript
// AI服务抽象层
interface AIService {
  generateResponse(prompt: string, options: GenerationOptions): Promise<string>;
  generateImage(prompt: string, options: ImageOptions): Promise<string>;
  embedText(text: string): Promise<number[]>;
}

// 多AI后端支持
class AIServiceManager {
  private services: Map<string, AIService> = new Map();
  
  registerService(name: string, service: AIService) {
    this.services.set(name, service);
  }
  
  async generate(serviceName: string, prompt: string, options: GenerationOptions) {
    const service = this.services.get(serviceName);
    if (!service) throw new Error(`Service ${serviceName} not found`);
    return await service.generateResponse(prompt, options);
  }
}
```

## 数据模型设计

### 核心实体
```typescript
// 用户模型
interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  preferences: UserPreferences;
  subscription: SubscriptionInfo;
  createdAt: Date;
  updatedAt: Date;
}

// 角色模型
interface Character {
  id: string;
  name: string;
  description: string;
  personality: string;
  avatar?: string;
  tags: string[];
  isPublic: boolean;
  authorId: string;
  downloadCount: number;
  rating: number;
  // SillyTavern兼容字段
  cardData: CharacterCard;
  createdAt: Date;
  updatedAt: Date;
}

// 对话模型
interface Conversation {
  id: string;
  characterIds: string[];
  userId: string;
  messages: Message[];
  settings: ConversationSettings;
  createdAt: Date;
  updatedAt: Date;
}

// 消息模型
interface Message {
  id: string;
  conversationId: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  metadata?: MessageMetadata;
  timestamp: Date;
}

// 世界观模型
interface World {
  id: string;
  name: string;
  description: string;
  thumbnail?: string;
  lorebook: LorebookEntry[];
  timeline: TimelineEvent[];
  locations: Location[];
  authorId: string;
  isPublic: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// 小说模型
interface Novel {
  id: string;
  title: string;
  description: string;
  cover?: string;
  chapters: Chapter[];
  worldId?: string;
  authorId: string;
  genre: string[];
  isInteractive: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

## 性能优化策略

### 1. 渲染优化
- React.memo和useMemo优化重渲染
- FlatList虚拟化长列表
- 图片懒加载和缓存策略
- 代码分割和动态导入

### 2. 数据管理
- SQLite本地缓存
- 增量同步机制
- 离线优先策略
- 数据预加载和预测性缓存

### 3. AI服务优化
- 请求队列和并发控制
- 响应流式处理
- 本地模型集成选项
- 智能缓存和去重

## 安全和隐私

### 数据保护
- 端到端加密存储
- API密钥安全管理
- 用户数据匿名化
- GDPR合规性支持

### 内容审核
- AI内容过滤
- 用户举报机制
- 社区自治工具
- 年龄分级系统

## 部署和发布

### 平台支持
- iOS App Store
- Google Play Store
- Web版本 (PWA)
- 桌面版本 (Electron)

### CI/CD流程
- 自动化测试
- 代码质量检查
- 多平台构建
- 灰度发布策略

## 开发计划

### Phase 1: 核心功能 (3个月)
- 基础架构搭建
- 聊天功能实现
- SillyTavern兼容层
- 基础UI组件库

### Phase 2: 内容生态 (2个月)
- 发现和创作功能
- 社区论坛系统
- 内容分享机制

### Phase 3: 高级功能 (2个月)
- 世界观系统
- 互动小说引擎
- AI辅助创作工具

### Phase 4: 优化和扩展 (持续)
- 性能优化
- 新功能迭代
- 社区运营支持

## 技术债务和风险

### 主要风险
1. AI服务依赖和成本控制
2. 大规模用户数据同步
3. 内容版权和合规问题
4. 跨平台兼容性维护

### 缓解策略
1. 多AI后端支持和降级机制
2. 分层存储和智能同步
3. 完善的内容审核和法务支持
4. 持续的兼容性测试和更新

---

*本文档将随着项目进展持续更新和完善*
