# NEXTNOVEL APP API设计与数据库架构

## 数据库设计

### 核心表结构

#### 用户表 (users)
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  username VARCHAR(50) UNIQUE NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  avatar_url TEXT,
  display_name VARCHAR(100),
  bio TEXT,
  subscription_tier VARCHAR(20) DEFAULT 'free',
  subscription_expires_at TIMESTAMP,
  preferences JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  last_active_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_subscription ON users(subscription_tier, subscription_expires_at);
```

#### 角色表 (characters)
```sql
CREATE TABLE characters (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  personality TEXT,
  scenario TEXT,
  first_message TEXT,
  example_dialogue TEXT,
  avatar_url TEXT,
  tags TEXT[] DEFAULT '{}',
  is_public BOOLEAN DEFAULT false,
  is_featured BOOLEAN DEFAULT false,
  author_id UUID REFERENCES users(id) ON DELETE CASCADE,
  download_count INTEGER DEFAULT 0,
  rating_average DECIMAL(3,2) DEFAULT 0,
  rating_count INTEGER DEFAULT 0,
  -- SillyTavern兼容字段
  card_data JSONB NOT NULL,
  character_book JSONB,
  -- 元数据
  version VARCHAR(20) DEFAULT '1.0.0',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_characters_author ON characters(author_id);
CREATE INDEX idx_characters_public ON characters(is_public) WHERE is_public = true;
CREATE INDEX idx_characters_featured ON characters(is_featured) WHERE is_featured = true;
CREATE INDEX idx_characters_tags ON characters USING GIN(tags);
CREATE INDEX idx_characters_rating ON characters(rating_average DESC, rating_count DESC);
CREATE INDEX idx_characters_downloads ON characters(download_count DESC);
```

#### 对话表 (conversations)
```sql
CREATE TABLE conversations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  character_ids UUID[] NOT NULL,
  title VARCHAR(255),
  is_group_chat BOOLEAN DEFAULT false,
  settings JSONB DEFAULT '{}',
  world_id UUID REFERENCES worlds(id) ON DELETE SET NULL,
  message_count INTEGER DEFAULT 0,
  last_message_at TIMESTAMP DEFAULT NOW(),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_conversations_user ON conversations(user_id);
CREATE INDEX idx_conversations_last_message ON conversations(last_message_at DESC);
CREATE INDEX idx_conversations_characters ON conversations USING GIN(character_ids);
```

#### 消息表 (messages)
```sql
CREATE TABLE messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
  role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
  character_id UUID REFERENCES characters(id) ON DELETE SET NULL,
  content TEXT NOT NULL,
  metadata JSONB DEFAULT '{}',
  tokens_used INTEGER,
  generation_time_ms INTEGER,
  created_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_messages_conversation ON messages(conversation_id, created_at);
CREATE INDEX idx_messages_role ON messages(role);
```

#### 世界观表 (worlds)
```sql
CREATE TABLE worlds (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  thumbnail_url TEXT,
  author_id UUID REFERENCES users(id) ON DELETE CASCADE,
  is_public BOOLEAN DEFAULT false,
  is_featured BOOLEAN DEFAULT false,
  tags TEXT[] DEFAULT '{}',
  download_count INTEGER DEFAULT 0,
  rating_average DECIMAL(3,2) DEFAULT 0,
  rating_count INTEGER DEFAULT 0,
  -- 世界数据
  lorebook JSONB DEFAULT '[]',
  timeline JSONB DEFAULT '[]',
  locations JSONB DEFAULT '[]',
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_worlds_author ON worlds(author_id);
CREATE INDEX idx_worlds_public ON worlds(is_public) WHERE is_public = true;
CREATE INDEX idx_worlds_tags ON worlds USING GIN(tags);
```

#### 小说表 (novels)
```sql
CREATE TABLE novels (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(255) NOT NULL,
  description TEXT,
  cover_url TEXT,
  author_id UUID REFERENCES users(id) ON DELETE CASCADE,
  world_id UUID REFERENCES worlds(id) ON DELETE SET NULL,
  genre TEXT[] DEFAULT '{}',
  is_interactive BOOLEAN DEFAULT true,
  is_public BOOLEAN DEFAULT false,
  is_completed BOOLEAN DEFAULT false,
  chapter_count INTEGER DEFAULT 0,
  word_count INTEGER DEFAULT 0,
  download_count INTEGER DEFAULT 0,
  rating_average DECIMAL(3,2) DEFAULT 0,
  rating_count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_novels_author ON novels(author_id);
CREATE INDEX idx_novels_public ON novels(is_public) WHERE is_public = true;
CREATE INDEX idx_novels_genre ON novels USING GIN(genre);
CREATE INDEX idx_novels_world ON novels(world_id);
```

#### 章节表 (chapters)
```sql
CREATE TABLE chapters (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  novel_id UUID REFERENCES novels(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  content TEXT NOT NULL,
  chapter_number INTEGER NOT NULL,
  word_count INTEGER DEFAULT 0,
  is_published BOOLEAN DEFAULT false,
  -- 互动元素
  choices JSONB DEFAULT '[]',
  variables JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(novel_id, chapter_number)
);

CREATE INDEX idx_chapters_novel ON chapters(novel_id, chapter_number);
```

### 关系表

#### 用户收藏表 (user_favorites)
```sql
CREATE TABLE user_favorites (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  content_type VARCHAR(20) NOT NULL CHECK (content_type IN ('character', 'world', 'novel')),
  content_id UUID NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(user_id, content_type, content_id)
);

CREATE INDEX idx_user_favorites_user ON user_favorites(user_id, content_type);
CREATE INDEX idx_user_favorites_content ON user_favorites(content_type, content_id);
```

#### 评分表 (ratings)
```sql
CREATE TABLE ratings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  content_type VARCHAR(20) NOT NULL CHECK (content_type IN ('character', 'world', 'novel')),
  content_id UUID NOT NULL,
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  review TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(user_id, content_type, content_id)
);

CREATE INDEX idx_ratings_content ON ratings(content_type, content_id);
CREATE INDEX idx_ratings_user ON ratings(user_id);
```

#### 论坛表 (forum_posts)
```sql
CREATE TABLE forum_posts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  author_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  content TEXT NOT NULL,
  category VARCHAR(50) NOT NULL,
  tags TEXT[] DEFAULT '{}',
  is_pinned BOOLEAN DEFAULT false,
  is_locked BOOLEAN DEFAULT false,
  view_count INTEGER DEFAULT 0,
  reply_count INTEGER DEFAULT 0,
  last_reply_at TIMESTAMP DEFAULT NOW(),
  -- 内容嵌入
  embedded_content JSONB,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_forum_posts_category ON forum_posts(category, last_reply_at DESC);
CREATE INDEX idx_forum_posts_author ON forum_posts(author_id);
CREATE INDEX idx_forum_posts_tags ON forum_posts USING GIN(tags);
```

## API设计

### 认证与授权

#### JWT Token结构
```typescript
interface JWTPayload {
  userId: string;
  username: string;
  subscriptionTier: 'free' | 'premium' | 'pro';
  permissions: string[];
  iat: number;
  exp: number;
}
```

#### 认证端点
```typescript
// POST /api/auth/register
interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  inviteCode?: string;
}

interface RegisterResponse {
  user: User;
  token: string;
  refreshToken: string;
}

// POST /api/auth/login
interface LoginRequest {
  email: string;
  password: string;
}

interface LoginResponse {
  user: User;
  token: string;
  refreshToken: string;
}

// POST /api/auth/refresh
interface RefreshRequest {
  refreshToken: string;
}

interface RefreshResponse {
  token: string;
  refreshToken: string;
}
```

### 角色管理API

```typescript
// GET /api/characters
interface GetCharactersRequest {
  page?: number;
  limit?: number;
  search?: string;
  tags?: string[];
  authorId?: string;
  sortBy?: 'created' | 'updated' | 'downloads' | 'rating';
  sortOrder?: 'asc' | 'desc';
  isPublic?: boolean;
}

interface GetCharactersResponse {
  characters: Character[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// POST /api/characters
interface CreateCharacterRequest {
  name: string;
  description: string;
  personality: string;
  scenario?: string;
  firstMessage?: string;
  exampleDialogue?: string;
  avatar?: File;
  tags?: string[];
  isPublic?: boolean;
  cardData: CharacterCardV2;
}

interface CreateCharacterResponse {
  character: Character;
}

// PUT /api/characters/:id
interface UpdateCharacterRequest {
  name?: string;
  description?: string;
  personality?: string;
  scenario?: string;
  firstMessage?: string;
  exampleDialogue?: string;
  avatar?: File;
  tags?: string[];
  isPublic?: boolean;
  cardData?: CharacterCardV2;
}

// POST /api/characters/import
interface ImportCharacterRequest {
  file: File; // PNG或JSON文件
  isPublic?: boolean;
}

interface ImportCharacterResponse {
  character: Character;
  warnings?: string[];
}

// GET /api/characters/:id/export
interface ExportCharacterResponse {
  // 返回文件流
  file: Blob;
  filename: string;
  contentType: string;
}
```

### 对话管理API

```typescript
// GET /api/conversations
interface GetConversationsRequest {
  page?: number;
  limit?: number;
  characterId?: string;
  worldId?: string;
}

interface GetConversationsResponse {
  conversations: Conversation[];
  pagination: PaginationInfo;
}

// POST /api/conversations
interface CreateConversationRequest {
  characterIds: string[];
  title?: string;
  worldId?: string;
  settings?: ConversationSettings;
}

interface CreateConversationResponse {
  conversation: Conversation;
}

// POST /api/conversations/:id/messages
interface SendMessageRequest {
  content: string;
  role: 'user';
  metadata?: Record<string, any>;
}

interface SendMessageResponse {
  userMessage: Message;
  aiMessage: Message;
  usage: {
    tokensUsed: number;
    generationTimeMs: number;
  };
}

// GET /api/conversations/:id/messages
interface GetMessagesRequest {
  page?: number;
  limit?: number;
  before?: string; // message ID
  after?: string;  // message ID
}

interface GetMessagesResponse {
  messages: Message[];
  pagination: PaginationInfo;
}
```

### AI服务集成API

```typescript
// POST /api/ai/generate
interface GenerateRequest {
  prompt: string;
  characterId?: string;
  worldId?: string;
  settings: {
    model: string;
    temperature: number;
    maxTokens: number;
    topP: number;
    topK?: number;
    repetitionPenalty?: number;
    stopSequences?: string[];
  };
  context?: {
    conversationHistory: Message[];
    worldInfo: string[];
    characterInfo: string;
  };
}

interface GenerateResponse {
  content: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model: string;
  finishReason: 'stop' | 'length' | 'content_filter';
}

// POST /api/ai/generate-image
interface GenerateImageRequest {
  prompt: string;
  style?: string;
  size?: '512x512' | '1024x1024' | '1024x1792';
  quality?: 'standard' | 'hd';
}

interface GenerateImageResponse {
  imageUrl: string;
  revisedPrompt?: string;
}
```

### 内容发现API

```typescript
// GET /api/discovery/featured
interface GetFeaturedContentResponse {
  characters: Character[];
  worlds: World[];
  novels: Novel[];
  creators: User[];
}

// GET /api/discovery/trending
interface GetTrendingRequest {
  contentType: 'character' | 'world' | 'novel';
  timeframe: '24h' | '7d' | '30d';
  limit?: number;
}

interface GetTrendingResponse {
  items: (Character | World | Novel)[];
  timeframe: string;
}

// GET /api/discovery/recommendations
interface GetRecommendationsRequest {
  userId: string;
  contentType: 'character' | 'world' | 'novel';
  limit?: number;
}

interface GetRecommendationsResponse {
  items: (Character | World | Novel)[];
  reasons: string[];
}
```

### 论坛API

```typescript
// GET /api/forum/posts
interface GetForumPostsRequest {
  category?: string;
  tags?: string[];
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: 'created' | 'updated' | 'replies' | 'views';
}

interface GetForumPostsResponse {
  posts: ForumPost[];
  pagination: PaginationInfo;
  categories: string[];
}

// POST /api/forum/posts
interface CreateForumPostRequest {
  title: string;
  content: string;
  category: string;
  tags?: string[];
  embeddedContent?: {
    type: 'character' | 'world' | 'novel';
    id: string;
  };
}

interface CreateForumPostResponse {
  post: ForumPost;
}
```

## 缓存策略

### Redis缓存设计
```typescript
const CacheKeys = {
  // 用户相关
  user: (id: string) => `user:${id}`,
  userSessions: (id: string) => `user:${id}:sessions`,
  
  // 内容缓存
  character: (id: string) => `character:${id}`,
  characterList: (params: string) => `characters:${params}`,
  
  // 热门内容
  trending: (type: string, timeframe: string) => `trending:${type}:${timeframe}`,
  featured: () => 'featured:content',
  
  // AI生成缓存
  aiResponse: (hash: string) => `ai:response:${hash}`,
  
  // 统计数据
  stats: (type: string) => `stats:${type}`,
  
  // 搜索结果
  search: (query: string, filters: string) => `search:${query}:${filters}`
};

// 缓存TTL配置
const CacheTTL = {
  user: 3600,           // 1小时
  content: 1800,        // 30分钟
  trending: 900,        // 15分钟
  featured: 3600,       // 1小时
  aiResponse: 86400,    // 24小时
  search: 600,          // 10分钟
  stats: 300            // 5分钟
};
```

## 性能优化

### 数据库优化
```sql
-- 分区表（按时间分区消息表）
CREATE TABLE messages_2024_01 PARTITION OF messages
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- 物化视图（热门内容统计）
CREATE MATERIALIZED VIEW trending_characters AS
SELECT 
  c.*,
  COALESCE(d.download_count_24h, 0) as recent_downloads,
  COALESCE(r.rating_count_7d, 0) as recent_ratings
FROM characters c
LEFT JOIN (
  SELECT character_id, COUNT(*) as download_count_24h
  FROM user_downloads 
  WHERE created_at > NOW() - INTERVAL '24 hours'
  GROUP BY character_id
) d ON c.id = d.character_id
LEFT JOIN (
  SELECT content_id, COUNT(*) as rating_count_7d
  FROM ratings 
  WHERE content_type = 'character' 
    AND created_at > NOW() - INTERVAL '7 days'
  GROUP BY content_id
) r ON c.id = r.content_id
WHERE c.is_public = true
ORDER BY recent_downloads DESC, recent_ratings DESC;

-- 定期刷新物化视图
CREATE OR REPLACE FUNCTION refresh_trending_views()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY trending_characters;
  REFRESH MATERIALIZED VIEW CONCURRENTLY trending_worlds;
  REFRESH MATERIALIZED VIEW CONCURRENTLY trending_novels;
END;
$$ LANGUAGE plpgsql;

-- 创建定时任务
SELECT cron.schedule('refresh-trending', '*/15 * * * *', 'SELECT refresh_trending_views();');
```

### API限流配置
```typescript
const RateLimits = {
  // 认证相关
  auth: {
    login: { windowMs: 15 * 60 * 1000, max: 5 },      // 15分钟5次
    register: { windowMs: 60 * 60 * 1000, max: 3 }    // 1小时3次
  },
  
  // AI生成
  ai: {
    free: { windowMs: 60 * 1000, max: 10 },           // 1分钟10次
    premium: { windowMs: 60 * 1000, max: 50 },        // 1分钟50次
    pro: { windowMs: 60 * 1000, max: 200 }            // 1分钟200次
  },
  
  // 内容创建
  content: {
    create: { windowMs: 60 * 1000, max: 5 },          // 1分钟5次
    update: { windowMs: 60 * 1000, max: 10 },         // 1分钟10次
    delete: { windowMs: 60 * 1000, max: 3 }           // 1分钟3次
  },
  
  // 通用API
  general: { windowMs: 60 * 1000, max: 100 }          // 1分钟100次
};
```

通过以上详细的API设计和数据库架构，NEXTNOVEL APP将拥有强大、可扩展的后端基础设施，支持高并发用户访问和复杂的AI交互功能。
