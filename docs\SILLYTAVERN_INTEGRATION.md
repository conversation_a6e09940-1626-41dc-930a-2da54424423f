# SillyTavern生态集成详细方案

## 概述

NEXTNOVEL APP将完全兼容SillyTavern生态系统，支持角色卡、预设、正则表达式、世界信息等内容的无缝导入和使用。本文档详细说明了集成方案和实现细节。

## 角色卡 (Character Cards) 集成

### 支持格式
1. **Character Card V1格式**
2. **Character Card V2格式** (推荐)
3. **PNG嵌入式角色卡**
4. **JSON格式角色卡**

### 角色卡数据结构
```typescript
interface CharacterCardV2 {
  spec: 'chara_card_v2';
  spec_version: '2.0';
  data: {
    name: string;
    description: string;
    personality: string;
    scenario: string;
    first_mes: string;
    mes_example: string;
    creator_notes: string;
    system_prompt: string;
    post_history_instructions: string;
    alternate_greetings: string[];
    character_book?: CharacterBook;
    tags: string[];
    creator: string;
    character_version: string;
    extensions: Record<string, any>;
  };
}

interface CharacterBook {
  name?: string;
  description?: string;
  scan_depth?: number;
  token_budget?: number;
  recursive_scanning?: boolean;
  entries: WorldInfoEntry[];
}

interface WorldInfoEntry {
  keys: string[];
  content: string;
  extensions: {
    position: 'before_char' | 'after_char';
    depth: number;
    probability: number;
    useProbability: boolean;
  };
  enabled: boolean;
  insertion_order: number;
}
```

### PNG元数据解析
```typescript
class CharacterCardParser {
  static async parseFromPNG(file: File): Promise<CharacterCardV2> {
    const arrayBuffer = await file.arrayBuffer();
    const uint8Array = new Uint8Array(arrayBuffer);
    
    // 查找tEXt chunk中的chara数据
    const textChunks = this.extractTextChunks(uint8Array);
    const charaData = textChunks.find(chunk => chunk.keyword === 'chara');
    
    if (!charaData) {
      throw new Error('No character data found in PNG');
    }
    
    // Base64解码
    const decodedData = atob(charaData.text);
    return JSON.parse(decodedData);
  }
  
  static async parseFromJSON(file: File): Promise<CharacterCardV2> {
    const text = await file.text();
    const data = JSON.parse(text);
    
    // 兼容V1格式转换
    if (!data.spec || data.spec === 'chara_card_v1') {
      return this.convertV1ToV2(data);
    }
    
    return data;
  }
  
  private static convertV1ToV2(v1Data: any): CharacterCardV2 {
    return {
      spec: 'chara_card_v2',
      spec_version: '2.0',
      data: {
        name: v1Data.name || '',
        description: v1Data.description || '',
        personality: v1Data.personality || '',
        scenario: v1Data.scenario || '',
        first_mes: v1Data.first_mes || '',
        mes_example: v1Data.mes_example || '',
        creator_notes: '',
        system_prompt: '',
        post_history_instructions: '',
        alternate_greetings: [],
        tags: [],
        creator: '',
        character_version: '1.0.0',
        extensions: {}
      }
    };
  }
}
```

### 一键导入功能
```typescript
class CharacterImportService {
  async importCharacter(file: File): Promise<Character> {
    let cardData: CharacterCardV2;
    
    // 根据文件类型选择解析方法
    if (file.type === 'image/png') {
      cardData = await CharacterCardParser.parseFromPNG(file);
    } else if (file.type === 'application/json') {
      cardData = await CharacterCardParser.parseFromJSON(file);
    } else {
      throw new Error('Unsupported file format');
    }
    
    // 创建本地角色对象
    const character: Character = {
      id: generateUUID(),
      name: cardData.data.name,
      description: cardData.data.description,
      personality: cardData.data.personality,
      avatar: await this.extractAvatar(file),
      tags: cardData.data.tags,
      isPublic: false,
      authorId: getCurrentUserId(),
      downloadCount: 0,
      rating: 0,
      cardData: cardData,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    // 保存到本地数据库
    await CharacterStorage.save(character);
    
    // 处理角色书 (World Info)
    if (cardData.data.character_book) {
      await this.importCharacterBook(character.id, cardData.data.character_book);
    }
    
    return character;
  }
  
  private async extractAvatar(file: File): Promise<string | undefined> {
    if (file.type === 'image/png') {
      // PNG文件本身就是头像
      const base64 = await this.fileToBase64(file);
      return base64;
    }
    return undefined;
  }
  
  private async importCharacterBook(characterId: string, book: CharacterBook) {
    const worldInfo: WorldInfoEntry[] = book.entries.map(entry => ({
      id: generateUUID(),
      characterId,
      keys: entry.keys,
      content: entry.content,
      enabled: entry.enabled,
      insertionOrder: entry.insertion_order,
      position: entry.extensions.position,
      depth: entry.extensions.depth,
      probability: entry.extensions.probability,
      useProbability: entry.extensions.useProbability
    }));
    
    await WorldInfoStorage.saveEntries(worldInfo);
  }
}
```

## 预设系统 (Presets) 集成

### 预设数据结构
```typescript
interface ChatPreset {
  name: string;
  system_prompt: string;
  input_sequence: string;
  output_sequence: string;
  last_output_sequence: string;
  activation_regex: string;
  wrap: boolean;
  names: boolean;
  send_if_empty: string;
  // AI参数
  temperature: number;
  top_p: number;
  top_k: number;
  typical_p: number;
  tfs: number;
  top_a: number;
  repetition_penalty: number;
  repetition_penalty_range: number;
  max_tokens: number;
  // 扩展参数
  extensions: Record<string, any>;
}
```

### 预设导入和应用
```typescript
class PresetService {
  async importPreset(file: File): Promise<ChatPreset> {
    const text = await file.text();
    const presetData = JSON.parse(text);
    
    // 验证预设格式
    this.validatePreset(presetData);
    
    // 保存到本地
    await PresetStorage.save(presetData);
    
    return presetData;
  }
  
  async applyPreset(conversationId: string, presetName: string) {
    const preset = await PresetStorage.getByName(presetName);
    if (!preset) throw new Error('Preset not found');
    
    // 更新对话设置
    const settings: ConversationSettings = {
      systemPrompt: preset.system_prompt,
      inputSequence: preset.input_sequence,
      outputSequence: preset.output_sequence,
      temperature: preset.temperature,
      topP: preset.top_p,
      topK: preset.top_k,
      maxTokens: preset.max_tokens,
      repetitionPenalty: preset.repetition_penalty,
      // ... 其他参数
    };
    
    await ConversationStorage.updateSettings(conversationId, settings);
  }
}
```

## 正则表达式 (Regex) 集成

### 正则规则数据结构
```typescript
interface RegexRule {
  id: string;
  name: string;
  enabled: boolean;
  // 输入处理规则
  input?: {
    find: string;
    replace: string;
    flags: string;
  };
  // 输出处理规则
  output?: {
    find: string;
    replace: string;
    flags: string;
  };
  // 触发条件
  conditions?: {
    characterNames?: string[];
    worldNames?: string[];
    tags?: string[];
  };
  // 执行顺序
  order: number;
}
```

### 正则处理引擎
```typescript
class RegexProcessor {
  private rules: RegexRule[] = [];
  
  async loadRules(characterId?: string, worldId?: string) {
    this.rules = await RegexStorage.getRules({
      characterId,
      worldId,
      enabled: true
    });
    
    // 按执行顺序排序
    this.rules.sort((a, b) => a.order - b.order);
  }
  
  processInput(text: string, context: ProcessingContext): string {
    let processedText = text;
    
    for (const rule of this.rules) {
      if (!rule.input) continue;
      if (!this.shouldApplyRule(rule, context)) continue;
      
      try {
        const regex = new RegExp(rule.input.find, rule.input.flags);
        processedText = processedText.replace(regex, rule.input.replace);
      } catch (error) {
        console.warn(`Regex rule ${rule.name} failed:`, error);
      }
    }
    
    return processedText;
  }
  
  processOutput(text: string, context: ProcessingContext): string {
    let processedText = text;
    
    for (const rule of this.rules) {
      if (!rule.output) continue;
      if (!this.shouldApplyRule(rule, context)) continue;
      
      try {
        const regex = new RegExp(rule.output.find, rule.output.flags);
        processedText = processedText.replace(regex, rule.output.replace);
      } catch (error) {
        console.warn(`Regex rule ${rule.name} failed:`, error);
      }
    }
    
    return processedText;
  }
  
  private shouldApplyRule(rule: RegexRule, context: ProcessingContext): boolean {
    if (!rule.conditions) return true;
    
    // 检查角色名称条件
    if (rule.conditions.characterNames?.length) {
      if (!rule.conditions.characterNames.includes(context.characterName)) {
        return false;
      }
    }
    
    // 检查世界名称条件
    if (rule.conditions.worldNames?.length) {
      if (!context.worldName || !rule.conditions.worldNames.includes(context.worldName)) {
        return false;
      }
    }
    
    // 检查标签条件
    if (rule.conditions.tags?.length) {
      const hasMatchingTag = rule.conditions.tags.some(tag => 
        context.characterTags?.includes(tag) || context.worldTags?.includes(tag)
      );
      if (!hasMatchingTag) return false;
    }
    
    return true;
  }
}

interface ProcessingContext {
  characterName: string;
  characterTags?: string[];
  worldName?: string;
  worldTags?: string[];
  conversationId: string;
}
```

## 世界信息 (World Info/Lorebook) 集成

### 世界信息触发系统
```typescript
class WorldInfoProcessor {
  async processMessage(
    message: string, 
    characterId: string, 
    worldId?: string
  ): Promise<string[]> {
    // 获取相关的世界信息条目
    const entries = await this.getRelevantEntries(message, characterId, worldId);
    
    // 按优先级和深度排序
    const sortedEntries = this.sortEntriesByPriority(entries);
    
    // 构建注入内容
    const injectedContent: string[] = [];
    let tokenCount = 0;
    const maxTokens = 2000; // 可配置的token预算
    
    for (const entry of sortedEntries) {
      const entryTokens = this.estimateTokens(entry.content);
      if (tokenCount + entryTokens > maxTokens) break;
      
      injectedContent.push(entry.content);
      tokenCount += entryTokens;
    }
    
    return injectedContent;
  }
  
  private async getRelevantEntries(
    message: string, 
    characterId: string, 
    worldId?: string
  ): Promise<WorldInfoEntry[]> {
    const allEntries = await WorldInfoStorage.getEntries({
      characterId,
      worldId,
      enabled: true
    });
    
    const relevantEntries: WorldInfoEntry[] = [];
    const messageWords = message.toLowerCase().split(/\s+/);
    
    for (const entry of allEntries) {
      // 检查关键词匹配
      const hasMatchingKey = entry.keys.some(key => {
        const keyWords = key.toLowerCase().split(/\s+/);
        return keyWords.every(keyWord => 
          messageWords.some(messageWord => 
            messageWord.includes(keyWord) || keyWord.includes(messageWord)
          )
        );
      });
      
      if (hasMatchingKey) {
        // 概率检查
        if (entry.useProbability && Math.random() > entry.probability) {
          continue;
        }
        
        relevantEntries.push(entry);
      }
    }
    
    return relevantEntries;
  }
  
  private sortEntriesByPriority(entries: WorldInfoEntry[]): WorldInfoEntry[] {
    return entries.sort((a, b) => {
      // 首先按深度排序 (深度越小优先级越高)
      if (a.depth !== b.depth) {
        return a.depth - b.depth;
      }
      
      // 然后按插入顺序排序
      return a.insertionOrder - b.insertionOrder;
    });
  }
  
  private estimateTokens(text: string): number {
    // 简单的token估算 (实际应该使用tokenizer)
    return Math.ceil(text.length / 4);
  }
}
```

## 导出功能

### 角色卡导出
```typescript
class CharacterExportService {
  async exportToPNG(characterId: string): Promise<Blob> {
    const character = await CharacterStorage.getById(characterId);
    if (!character) throw new Error('Character not found');
    
    // 构建角色卡数据
    const cardData: CharacterCardV2 = character.cardData;
    
    // 序列化为JSON并Base64编码
    const jsonString = JSON.stringify(cardData);
    const base64Data = btoa(jsonString);
    
    // 创建PNG文件并嵌入元数据
    const pngBuffer = await this.createPNGWithMetadata(
      character.avatar || await this.generateDefaultAvatar(character.name),
      base64Data
    );
    
    return new Blob([pngBuffer], { type: 'image/png' });
  }
  
  async exportToJSON(characterId: string): Promise<Blob> {
    const character = await CharacterStorage.getById(characterId);
    if (!character) throw new Error('Character not found');
    
    const jsonString = JSON.stringify(character.cardData, null, 2);
    return new Blob([jsonString], { type: 'application/json' });
  }
  
  private async createPNGWithMetadata(imageData: string, metadata: string): Promise<ArrayBuffer> {
    // 实现PNG tEXt chunk注入
    // 这里需要实现PNG格式的二进制操作
    // 将metadata作为'chara'关键字的tEXt chunk插入PNG文件
    
    // 简化实现，实际需要完整的PNG编码器
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx?.drawImage(img, 0, 0);
        
        canvas.toBlob((blob) => {
          if (blob) {
            blob.arrayBuffer().then(resolve);
          }
        }, 'image/png');
      };
      img.src = imageData;
    });
  }
}
```

## 批量导入工具

```typescript
class BatchImportService {
  async importMultipleFiles(files: File[]): Promise<ImportResult[]> {
    const results: ImportResult[] = [];
    
    for (const file of files) {
      try {
        let result: ImportResult;
        
        if (file.name.endsWith('.json') && file.name.includes('preset')) {
          // 预设文件
          const preset = await PresetService.importPreset(file);
          result = { type: 'preset', name: preset.name, success: true };
        } else if (file.name.endsWith('.json') && file.name.includes('regex')) {
          // 正则规则文件
          const rules = await RegexService.importRules(file);
          result = { type: 'regex', name: `${rules.length} rules`, success: true };
        } else if (file.type === 'image/png' || file.name.endsWith('.json')) {
          // 角色卡文件
          const character = await CharacterImportService.importCharacter(file);
          result = { type: 'character', name: character.name, success: true };
        } else {
          result = { 
            type: 'unknown', 
            name: file.name, 
            success: false, 
            error: 'Unsupported file type' 
          };
        }
        
        results.push(result);
      } catch (error) {
        results.push({
          type: 'unknown',
          name: file.name,
          success: false,
          error: error.message
        });
      }
    }
    
    return results;
  }
}

interface ImportResult {
  type: 'character' | 'preset' | 'regex' | 'world' | 'unknown';
  name: string;
  success: boolean;
  error?: string;
}
```

## 兼容性测试

### 测试用例
1. **角色卡导入测试**
   - SillyTavern官方示例角色卡
   - 社区热门角色卡
   - 各种格式和版本的兼容性

2. **预设兼容性测试**
   - 官方预设文件
   - 社区自定义预设
   - 参数范围和默认值处理

3. **正则表达式测试**
   - 常用正则规则集
   - 复杂嵌套规则
   - 性能和安全性测试

4. **世界信息测试**
   - 大型Lorebook导入
   - 关键词匹配准确性
   - Token预算管理

通过以上详细的集成方案，NEXTNOVEL APP将能够完全兼容SillyTavern生态系统，为用户提供无缝的迁移和使用体验。
