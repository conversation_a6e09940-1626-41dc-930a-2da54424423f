# NEXTNOVEL APP UI/UX设计规范

## 设计理念

### 核心原则
1. **沉浸式体验优先 (Immersion-First)**
   - UI元素在不使用时自动隐藏或最小化
   - 内容为王，界面为内容服务
   - 减少不必要的视觉干扰和中断

2. **创作与消费的无缝闭环**
   - 从消费到创作的路径最短化
   - 一键操作和智能推荐
   - 降低创作门槛

3. **AI作为创作伙伴**
   - AI辅助功能人格化
   - 协作式而非工具式交互
   - 用户始终保持主导权

4. **社区驱动的内容生态**
   - 突出创作者和作品
   - 内容即社交货币
   - 协作式创作支持

## 视觉设计系统

### 色彩方案
```typescript
// 主色调 - 深邃神秘感
const ColorPalette = {
  // 主色
  primary: {
    50: '#f0f4ff',
    100: '#e0e7ff', 
    500: '#6366f1',  // 主品牌色
    600: '#5b21b6',
    900: '#312e81'
  },
  
  // 辅助色 - 温暖创意感
  secondary: {
    50: '#fef7ff',
    100: '#fce7f3',
    500: '#ec4899',  // 强调色
    600: '#db2777',
    900: '#831843'
  },
  
  // 中性色 - 现代简约
  neutral: {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#e5e5e5',
    300: '#d4d4d4',
    400: '#a3a3a3',
    500: '#737373',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717'
  },
  
  // 功能色
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  info: '#3b82f6'
};

// 暗色主题
const DarkTheme = {
  background: {
    primary: '#0a0a0a',
    secondary: '#1a1a1a',
    tertiary: '#2a2a2a'
  },
  text: {
    primary: '#ffffff',
    secondary: '#a3a3a3',
    tertiary: '#737373'
  }
};
```

### 字体系统
```typescript
const Typography = {
  // 字体族
  fontFamily: {
    sans: ['Inter', 'SF Pro Display', 'system-ui', 'sans-serif'],
    serif: ['Crimson Text', 'Georgia', 'serif'],
    mono: ['JetBrains Mono', 'Monaco', 'monospace']
  },
  
  // 字体大小
  fontSize: {
    xs: '12px',
    sm: '14px',
    base: '16px',
    lg: '18px',
    xl: '20px',
    '2xl': '24px',
    '3xl': '30px',
    '4xl': '36px'
  },
  
  // 行高
  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75
  },
  
  // 字重
  fontWeight: {
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700
  }
};
```

### 间距系统
```typescript
const Spacing = {
  0: '0px',
  1: '4px',
  2: '8px',
  3: '12px',
  4: '16px',
  5: '20px',
  6: '24px',
  8: '32px',
  10: '40px',
  12: '48px',
  16: '64px',
  20: '80px',
  24: '96px'
};
```

## 组件设计规范

### 基础组件

#### 按钮 (Button)
```typescript
interface ButtonProps {
  variant: 'primary' | 'secondary' | 'ghost' | 'danger';
  size: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  icon?: ReactNode;
  children: ReactNode;
}

// 设计规范
const ButtonStyles = {
  primary: {
    backgroundColor: ColorPalette.primary[500],
    color: 'white',
    borderRadius: '12px',
    padding: '12px 24px',
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
    // 悬浮效果
    shadow: '0 4px 12px rgba(99, 102, 241, 0.3)',
    hoverShadow: '0 8px 24px rgba(99, 102, 241, 0.4)'
  },
  
  secondary: {
    backgroundColor: 'transparent',
    color: ColorPalette.primary[500],
    border: `2px solid ${ColorPalette.primary[500]}`,
    borderRadius: '12px'
  },
  
  ghost: {
    backgroundColor: 'transparent',
    color: ColorPalette.neutral[600],
    borderRadius: '8px',
    padding: '8px 16px'
  }
};
```

#### 卡片 (Card)
```typescript
interface CardProps {
  variant: 'character' | 'novel' | 'world' | 'post';
  interactive?: boolean;
  featured?: boolean;
  children: ReactNode;
}

// 角色卡片设计
const CharacterCardStyles = {
  container: {
    backgroundColor: 'white',
    borderRadius: '16px',
    padding: '16px',
    shadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
    // 交互效果
    transform: 'scale(1)',
    transition: 'all 0.2s ease',
    hoverTransform: 'scale(1.02)',
    hoverShadow: '0 8px 24px rgba(0, 0, 0, 0.15)'
  },
  
  avatar: {
    width: '64px',
    height: '64px',
    borderRadius: '12px',
    marginBottom: '12px'
  },
  
  name: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    color: ColorPalette.neutral[900],
    marginBottom: '4px'
  },
  
  description: {
    fontSize: Typography.fontSize.sm,
    color: ColorPalette.neutral[600],
    lineHeight: Typography.lineHeight.normal,
    // 文本截断
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    display: '-webkit-box',
    WebkitLineClamp: 2,
    WebkitBoxOrient: 'vertical'
  }
};
```

### 导航组件

#### 底部导航 (TabBar)
```typescript
const TabBarStyles = {
  container: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    backdropFilter: 'blur(20px)',
    borderTopWidth: '1px',
    borderTopColor: ColorPalette.neutral[200],
    paddingBottom: '34px', // iPhone安全区域
    height: '88px'
  },
  
  tab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: '8px'
  },
  
  icon: {
    width: '24px',
    height: '24px',
    marginBottom: '4px'
  },
  
  label: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.medium
  },
  
  // 活跃状态
  activeTab: {
    color: ColorPalette.primary[500]
  },
  
  inactiveTab: {
    color: ColorPalette.neutral[400]
  }
};
```

#### 顶部导航 (Header)
```typescript
const HeaderStyles = {
  container: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    backdropFilter: 'blur(20px)',
    paddingTop: '44px', // 状态栏高度
    paddingHorizontal: '16px',
    paddingBottom: '12px',
    borderBottomWidth: '1px',
    borderBottomColor: ColorPalette.neutral[100]
  },
  
  title: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
    color: ColorPalette.neutral[900],
    textAlign: 'center'
  },
  
  backButton: {
    position: 'absolute',
    left: '16px',
    top: '50px',
    width: '32px',
    height: '32px',
    borderRadius: '8px',
    backgroundColor: ColorPalette.neutral[100],
    alignItems: 'center',
    justifyContent: 'center'
  }
};
```

## 页面布局规范

### 首页 (Home) 布局

#### 聊天页面
```typescript
const ChatLayoutStyles = {
  // 角色库网格
  characterGrid: {
    padding: '16px',
    gap: '12px',
    numColumns: 2, // 响应式：手机2列，平板3-4列
  },
  
  // 对话界面
  chatContainer: {
    flex: 1,
    backgroundColor: ColorPalette.neutral[50]
  },
  
  messageList: {
    flex: 1,
    padding: '16px',
    paddingBottom: '100px' // 为输入框留空间
  },
  
  inputContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    padding: '16px',
    borderTopWidth: '1px',
    borderTopColor: ColorPalette.neutral[200],
    // 安全区域适配
    paddingBottom: '34px'
  }
};
```

#### 消息气泡设计
```typescript
const MessageBubbleStyles = {
  userMessage: {
    alignSelf: 'flex-end',
    backgroundColor: ColorPalette.primary[500],
    color: 'white',
    borderRadius: '18px',
    borderBottomRightRadius: '4px',
    padding: '12px 16px',
    marginBottom: '8px',
    maxWidth: '80%',
    shadow: '0 2px 8px rgba(99, 102, 241, 0.2)'
  },
  
  aiMessage: {
    alignSelf: 'flex-start',
    backgroundColor: 'white',
    color: ColorPalette.neutral[900],
    borderRadius: '18px',
    borderBottomLeftRadius: '4px',
    padding: '12px 16px',
    marginBottom: '8px',
    maxWidth: '80%',
    shadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
  },
  
  // 打字效果
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: '12px 16px',
    backgroundColor: ColorPalette.neutral[100],
    borderRadius: '18px',
    marginBottom: '8px'
  }
};
```

### 发现页面 (Discovery) 布局
```typescript
const DiscoveryLayoutStyles = {
  // 今日焦点轮播
  featuredCarousel: {
    height: '200px',
    marginBottom: '24px'
  },
  
  // 分类标签
  categoryTabs: {
    flexDirection: 'row',
    paddingHorizontal: '16px',
    marginBottom: '16px',
    gap: '8px'
  },
  
  categoryTab: {
    paddingHorizontal: '16px',
    paddingVertical: '8px',
    borderRadius: '20px',
    backgroundColor: ColorPalette.neutral[100]
  },
  
  activeCategoryTab: {
    backgroundColor: ColorPalette.primary[500],
    color: 'white'
  },
  
  // 内容网格
  contentGrid: {
    padding: '16px',
    gap: '16px'
  }
};
```

### 创作页面 (Creation) 布局
```typescript
const CreationLayoutStyles = {
  // 创作模式选择
  modeSelector: {
    flexDirection: 'row',
    padding: '16px',
    gap: '12px'
  },
  
  modeCard: {
    flex: 1,
    padding: '20px',
    borderRadius: '16px',
    backgroundColor: 'white',
    alignItems: 'center',
    border: `2px solid ${ColorPalette.neutral[200]}`
  },
  
  selectedModeCard: {
    borderColor: ColorPalette.primary[500],
    backgroundColor: ColorPalette.primary[50]
  },
  
  // 表单布局
  formContainer: {
    padding: '16px',
    gap: '16px'
  },
  
  formField: {
    marginBottom: '16px'
  },
  
  label: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
    color: ColorPalette.neutral[700],
    marginBottom: '8px'
  },
  
  input: {
    borderWidth: '2px',
    borderColor: ColorPalette.neutral[200],
    borderRadius: '12px',
    padding: '12px 16px',
    fontSize: Typography.fontSize.base,
    backgroundColor: 'white'
  },
  
  focusedInput: {
    borderColor: ColorPalette.primary[500],
    shadow: '0 0 0 3px rgba(99, 102, 241, 0.1)'
  }
};
```

## 交互设计规范

### 手势操作
```typescript
const GesturePatterns = {
  // 卡片交互
  cardPress: {
    onPress: '进入详情/开始使用',
    onLongPress: '显示快捷菜单',
    onSwipeLeft: '快捷操作（收藏/删除）',
    onSwipeRight: '快捷操作（分享/编辑）'
  },
  
  // 聊天界面
  chatGestures: {
    onMessageLongPress: '显示消息选项（复制/删除/重新生成）',
    onSwipeDown: '刷新对话',
    onSwipeUp: '显示更多选项'
  },
  
  // 阅读界面
  readingGestures: {
    onTap: '显示/隐藏控制栏',
    onSwipeLeft: '下一页',
    onSwipeRight: '上一页',
    onPinch: '调整字体大小'
  }
};
```

### 动画效果
```typescript
const AnimationSpecs = {
  // 页面转场
  pageTransition: {
    type: 'spring',
    damping: 20,
    stiffness: 100,
    duration: 300
  },
  
  // 卡片悬浮
  cardHover: {
    scale: 1.02,
    shadowOpacity: 0.15,
    duration: 200,
    easing: 'ease-out'
  },
  
  // 按钮点击
  buttonPress: {
    scale: 0.95,
    duration: 100,
    easing: 'ease-in-out'
  },
  
  // 消息出现
  messageAppear: {
    opacity: [0, 1],
    translateY: [20, 0],
    duration: 300,
    easing: 'ease-out'
  },
  
  // 打字效果
  typingEffect: {
    opacity: [0.3, 1, 0.3],
    duration: 1000,
    repeat: -1,
    easing: 'ease-in-out'
  }
};
```

### 反馈机制
```typescript
const FeedbackPatterns = {
  // 触觉反馈
  haptic: {
    light: 'Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)',
    medium: 'Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium)',
    heavy: 'Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy)',
    success: 'Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success)',
    error: 'Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error)'
  },
  
  // 视觉反馈
  visual: {
    loading: '骨架屏 + 脉冲动画',
    success: '绿色勾选图标 + 缩放动画',
    error: '红色感叹号 + 摇摆动画',
    processing: '旋转加载器 + 进度条'
  },
  
  // 音效反馈
  audio: {
    buttonTap: 'subtle_click.wav',
    messageReceived: 'message_pop.wav',
    success: 'success_chime.wav',
    error: 'error_buzz.wav'
  }
};
```

## 响应式设计

### 断点系统
```typescript
const Breakpoints = {
  mobile: '0px',      // 手机
  tablet: '768px',    // 平板
  desktop: '1024px',  // 桌面
  wide: '1440px'      // 宽屏
};

// 响应式布局
const ResponsiveLayout = {
  characterGrid: {
    mobile: { numColumns: 2, gap: 12 },
    tablet: { numColumns: 3, gap: 16 },
    desktop: { numColumns: 4, gap: 20 }
  },
  
  chatLayout: {
    mobile: { maxWidth: '100%', padding: 16 },
    tablet: { maxWidth: '600px', padding: 24 },
    desktop: { maxWidth: '800px', padding: 32 }
  }
};
```

### 暗色主题适配
```typescript
const DarkModeStyles = {
  // 自动切换逻辑
  useColorScheme: 'Appearance.getColorScheme()',
  
  // 暗色变体
  darkVariants: {
    background: DarkTheme.background,
    text: DarkTheme.text,
    card: {
      backgroundColor: DarkTheme.background.secondary,
      borderColor: ColorPalette.neutral[700]
    },
    button: {
      primary: ColorPalette.primary[400], // 暗色下使用较亮的主色
      secondary: ColorPalette.neutral[600]
    }
  }
};
```

## 可访问性 (Accessibility)

### 无障碍设计
```typescript
const AccessibilityFeatures = {
  // 语义化标签
  semanticLabels: {
    button: 'accessibilityRole="button"',
    heading: 'accessibilityRole="header"',
    text: 'accessibilityRole="text"'
  },
  
  // 屏幕阅读器支持
  screenReader: {
    accessibilityLabel: '描述性文本',
    accessibilityHint: '操作提示',
    accessibilityValue: '当前值'
  },
  
  // 对比度要求
  colorContrast: {
    normal: '4.5:1', // WCAG AA标准
    large: '3:1'     // 大字体
  },
  
  // 触摸目标大小
  touchTarget: {
    minimum: '44px', // iOS HIG标准
    recommended: '48px' // Material Design标准
  }
};
```

通过以上详细的UI/UX设计规范，NEXTNOVEL APP将提供一致、美观、易用的用户体验，充分体现"沉浸式体验优先"的设计理念。
