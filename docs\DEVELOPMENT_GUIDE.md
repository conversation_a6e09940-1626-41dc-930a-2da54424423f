# NEXTNOVEL APP 开发指南

## 开发环境搭建

### 系统要求
- **Node.js**: 18.0.0 或更高版本
- **npm**: 9.0.0 或更高版本
- **Git**: 最新版本
- **Android Studio**: (Android开发)
- **Xcode**: (iOS开发，仅macOS)

### 环境配置

#### 1. 克隆项目
```bash
git clone https://github.com/your-org/nextnovel.git
cd nextnovel
```

#### 2. 安装依赖
```bash
npm install
```

#### 3. 环境变量配置
创建 `.env` 文件：
```bash
# API配置
API_BASE_URL=http://localhost:3000/api
WEBSOCKET_URL=ws://localhost:3000

# AI服务配置
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
GOOGLE_AI_API_KEY=your_google_key

# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/nextnovel
REDIS_URL=redis://localhost:6379

# 文件存储
AWS_ACCESS_KEY_ID=your_aws_key
AWS_SECRET_ACCESS_KEY=your_aws_secret
AWS_S3_BUCKET=nextnovel-assets
AWS_REGION=us-east-1

# 推送通知
EXPO_PUSH_TOKEN=your_expo_token

# 分析和监控
SENTRY_DSN=your_sentry_dsn
ANALYTICS_API_KEY=your_analytics_key
```

#### 4. 数据库初始化
```bash
# 启动PostgreSQL和Redis
docker-compose up -d postgres redis

# 运行数据库迁移
npm run db:migrate

# 填充测试数据
npm run db:seed
```

### 开发服务器启动

#### 前端开发
```bash
# 启动Expo开发服务器
npm start

# 特定平台
npm run android    # Android模拟器
npm run ios        # iOS模拟器
npm run web        # Web浏览器
```

#### 后端开发
```bash
# 启动API服务器
npm run server:dev

# 启动WebSocket服务
npm run websocket:dev

# 启动后台任务处理器
npm run worker:dev
```

## 项目结构详解

### 前端架构
```
app/
├── (tabs)/                 # 底部导航页面
│   ├── home/              # 首页模块
│   │   ├── chat/          # 聊天功能
│   │   ├── novel/         # 小说阅读
│   │   └── world/         # 世界探索
│   ├── discovery/         # 发现页面
│   ├── creation/          # 创作工具
│   ├── forum/             # 社区论坛
│   └── profile/           # 个人中心
├── modals/                # 模态页面
├── _layout.tsx            # 根布局
└── +not-found.tsx         # 404页面

components/
├── ui/                    # 基础UI组件
│   ├── Button.tsx
│   ├── Card.tsx
│   ├── Input.tsx
│   ├── Modal.tsx
│   └── index.ts
├── chat/                  # 聊天组件
│   ├── MessageBubble.tsx
│   ├── ChatInput.tsx
│   ├── TypingIndicator.tsx
│   └── CharacterAvatar.tsx
├── creation/              # 创作组件
│   ├── CharacterEditor.tsx
│   ├── WorldBuilder.tsx
│   ├── NovelEditor.tsx
│   └── AIAssistant.tsx
└── shared/                # 共享组件
    ├── LoadingSpinner.tsx
    ├── ErrorBoundary.tsx
    └── EmptyState.tsx

services/
├── api/                   # API服务
│   ├── client.ts          # HTTP客户端
│   ├── auth.ts            # 认证服务
│   ├── characters.ts      # 角色管理
│   ├── conversations.ts   # 对话管理
│   └── ai.ts              # AI服务
├── storage/               # 本地存储
│   ├── database.ts        # SQLite数据库
│   ├── cache.ts           # 缓存管理
│   └── files.ts           # 文件管理
├── sync/                  # 数据同步
│   ├── syncManager.ts     # 同步管理器
│   ├── conflictResolver.ts # 冲突解决
│   └── offlineQueue.ts    # 离线队列
└── sillytavern/           # SillyTavern兼容
    ├── parser.ts          # 格式解析
    ├── converter.ts       # 格式转换
    └── validator.ts       # 数据验证
```

### 状态管理架构
```typescript
// contexts/AppContext.tsx
interface AppState {
  user: User | null;
  characters: Character[];
  conversations: Conversation[];
  worlds: World[];
  novels: Novel[];
  ui: UIState;
  sync: SyncState;
}

interface AppActions {
  // 用户操作
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => Promise<void>;
  updateProfile: (profile: Partial<User>) => Promise<void>;
  
  // 角色操作
  createCharacter: (character: CreateCharacterRequest) => Promise<Character>;
  updateCharacter: (id: string, updates: Partial<Character>) => Promise<Character>;
  deleteCharacter: (id: string) => Promise<void>;
  importCharacter: (file: File) => Promise<Character>;
  
  // 对话操作
  createConversation: (request: CreateConversationRequest) => Promise<Conversation>;
  sendMessage: (conversationId: string, content: string) => Promise<Message>;
  deleteConversation: (id: string) => Promise<void>;
  
  // UI操作
  setTheme: (theme: 'light' | 'dark' | 'auto') => void;
  showModal: (modal: ModalConfig) => void;
  hideModal: () => void;
  showToast: (toast: ToastConfig) => void;
}

// 使用Context + useReducer实现状态管理
const AppContext = createContext<{
  state: AppState;
  actions: AppActions;
} | null>(null);

export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within AppProvider');
  }
  return context;
};
```

## 开发规范

### 代码风格
```typescript
// .eslintrc.js
module.exports = {
  extends: [
    'expo',
    '@typescript-eslint/recommended',
    'prettier'
  ],
  rules: {
    // 强制使用TypeScript严格模式
    '@typescript-eslint/no-explicit-any': 'error',
    '@typescript-eslint/explicit-function-return-type': 'warn',
    
    // React规范
    'react-hooks/exhaustive-deps': 'error',
    'react/prop-types': 'off',
    
    // 命名规范
    '@typescript-eslint/naming-convention': [
      'error',
      {
        selector: 'interface',
        format: ['PascalCase'],
        prefix: ['I']
      },
      {
        selector: 'typeAlias',
        format: ['PascalCase']
      },
      {
        selector: 'enum',
        format: ['PascalCase']
      }
    ],
    
    // 导入规范
    'import/order': [
      'error',
      {
        groups: [
          'builtin',
          'external',
          'internal',
          'parent',
          'sibling',
          'index'
        ],
        'newlines-between': 'always'
      }
    ]
  }
};
```

### 组件开发规范
```typescript
// 组件模板
interface IButtonProps {
  variant: 'primary' | 'secondary' | 'ghost';
  size: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onPress: () => void;
  children: React.ReactNode;
}

export const Button: React.FC<IButtonProps> = ({
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  onPress,
  children
}) => {
  const styles = useButtonStyles({ variant, size, disabled });
  
  const handlePress = useCallback(() => {
    if (disabled || loading) return;
    
    // 触觉反馈
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    onPress();
  }, [disabled, loading, onPress]);
  
  return (
    <Pressable
      style={({ pressed }) => [
        styles.button,
        pressed && styles.pressed
      ]}
      onPress={handlePress}
      disabled={disabled || loading}
      accessibilityRole="button"
      accessibilityState={{ disabled: disabled || loading }}
    >
      {loading ? (
        <ActivityIndicator size="small" color={styles.text.color} />
      ) : (
        <Text style={styles.text}>{children}</Text>
      )}
    </Pressable>
  );
};

// 样式Hook
const useButtonStyles = ({ variant, size, disabled }: StyleProps) => {
  const theme = useTheme();
  
  return useMemo(() => StyleSheet.create({
    button: {
      borderRadius: theme.borderRadius.md,
      paddingHorizontal: theme.spacing[size === 'sm' ? 3 : size === 'lg' ? 6 : 4],
      paddingVertical: theme.spacing[size === 'sm' ? 2 : size === 'lg' ? 4 : 3],
      backgroundColor: getBackgroundColor(variant, theme),
      opacity: disabled ? 0.6 : 1,
      ...getShadow(variant, theme)
    },
    pressed: {
      transform: [{ scale: 0.95 }]
    },
    text: {
      fontSize: theme.fontSize[size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : 'base'],
      fontWeight: theme.fontWeight.medium,
      color: getTextColor(variant, theme),
      textAlign: 'center'
    }
  }), [variant, size, disabled, theme]);
};
```

### API服务开发
```typescript
// services/api/base.ts
class APIClient {
  private baseURL: string;
  private token: string | null = null;
  
  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }
  
  setToken(token: string): void {
    this.token = token;
  }
  
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers
    };
    
    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }
    
    try {
      const response = await fetch(url, {
        ...options,
        headers
      });
      
      if (!response.ok) {
        throw new APIError(
          response.status,
          await response.text()
        );
      }
      
      return await response.json();
    } catch (error) {
      if (error instanceof APIError) {
        throw error;
      }
      
      throw new APIError(0, 'Network error');
    }
  }
  
  async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' });
  }
  
  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined
    });
  }
  
  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined
    });
  }
  
  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

// 错误处理
class APIError extends Error {
  constructor(
    public status: number,
    message: string
  ) {
    super(message);
    this.name = 'APIError';
  }
}

// 使用示例
const apiClient = new APIClient(process.env.API_BASE_URL!);

export const charactersAPI = {
  getAll: (params?: GetCharactersRequest) => 
    apiClient.get<GetCharactersResponse>(`/characters?${new URLSearchParams(params)}`),
  
  getById: (id: string) => 
    apiClient.get<Character>(`/characters/${id}`),
  
  create: (data: CreateCharacterRequest) => 
    apiClient.post<CreateCharacterResponse>('/characters', data),
  
  update: (id: string, data: UpdateCharacterRequest) => 
    apiClient.put<Character>(`/characters/${id}`, data),
  
  delete: (id: string) => 
    apiClient.delete(`/characters/${id}`)
};
```

## 测试策略

### 单元测试
```typescript
// __tests__/components/Button.test.tsx
import { render, fireEvent } from '@testing-library/react-native';
import { Button } from '@/components/ui/Button';

describe('Button Component', () => {
  it('renders correctly with default props', () => {
    const { getByText } = render(
      <Button onPress={() => {}}>
        Test Button
      </Button>
    );
    
    expect(getByText('Test Button')).toBeTruthy();
  });
  
  it('calls onPress when pressed', () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(
      <Button onPress={mockOnPress}>
        Test Button
      </Button>
    );
    
    fireEvent.press(getByText('Test Button'));
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });
  
  it('does not call onPress when disabled', () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(
      <Button onPress={mockOnPress} disabled>
        Test Button
      </Button>
    );
    
    fireEvent.press(getByText('Test Button'));
    expect(mockOnPress).not.toHaveBeenCalled();
  });
});
```

### 集成测试
```typescript
// __tests__/integration/chat.test.tsx
import { renderWithProviders } from '@/test-utils';
import { ChatScreen } from '@/app/(tabs)/home/<USER>';
import { mockCharacter, mockConversation } from '@/test-utils/mocks';

describe('Chat Integration', () => {
  it('sends message and receives AI response', async () => {
    const { getByTestId, getByText } = renderWithProviders(
      <ChatScreen />,
      {
        initialState: {
          characters: [mockCharacter],
          conversations: [mockConversation]
        }
      }
    );
    
    const input = getByTestId('chat-input');
    const sendButton = getByTestId('send-button');
    
    fireEvent.changeText(input, 'Hello!');
    fireEvent.press(sendButton);
    
    // 等待AI响应
    await waitFor(() => {
      expect(getByText('Hello!')).toBeTruthy();
    });
    
    await waitFor(() => {
      expect(getByText(/AI response/)).toBeTruthy();
    });
  });
});
```

### E2E测试
```typescript
// e2e/chat-flow.e2e.ts
import { by, device, element, expect } from 'detox';

describe('Chat Flow', () => {
  beforeAll(async () => {
    await device.launchApp();
  });
  
  beforeEach(async () => {
    await device.reloadReactNative();
  });
  
  it('should complete full chat flow', async () => {
    // 导航到聊天页面
    await element(by.id('home-tab')).tap();
    await element(by.id('chat-tab')).tap();
    
    // 选择角色
    await element(by.id('character-card-0')).tap();
    
    // 发送消息
    await element(by.id('chat-input')).typeText('Hello there!');
    await element(by.id('send-button')).tap();
    
    // 验证消息发送
    await expect(element(by.text('Hello there!'))).toBeVisible();
    
    // 等待AI响应
    await waitFor(element(by.id('ai-message')))
      .toBeVisible()
      .withTimeout(10000);
  });
});
```

## 调试工具

### React Native调试
```typescript
// 开发工具配置
if (__DEV__) {
  // Flipper集成
  import('react-native-flipper').then(({ default: Flipper }) => {
    Flipper.addPlugin({
      getId() { return 'NextNovelDebugger'; },
      onConnect(connection) {
        // 调试连接建立
        console.log('Flipper connected');
      },
      onDisconnect() {
        console.log('Flipper disconnected');
      },
      runInBackground() {
        return false;
      }
    });
  });
  
  // Redux DevTools (如果使用Redux)
  const composeEnhancers = 
    (window as any).__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ || compose;
}

// 性能监控
import { Performance } from 'react-native-performance';

Performance.mark('app-start');
Performance.measure('app-startup', 'app-start');
```

### 日志系统
```typescript
// utils/logger.ts
enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

class Logger {
  private level: LogLevel = __DEV__ ? LogLevel.DEBUG : LogLevel.INFO;
  
  debug(message: string, ...args: any[]): void {
    if (this.level <= LogLevel.DEBUG) {
      console.log(`[DEBUG] ${message}`, ...args);
    }
  }
  
  info(message: string, ...args: any[]): void {
    if (this.level <= LogLevel.INFO) {
      console.info(`[INFO] ${message}`, ...args);
    }
  }
  
  warn(message: string, ...args: any[]): void {
    if (this.level <= LogLevel.WARN) {
      console.warn(`[WARN] ${message}`, ...args);
    }
  }
  
  error(message: string, error?: Error, ...args: any[]): void {
    if (this.level <= LogLevel.ERROR) {
      console.error(`[ERROR] ${message}`, error, ...args);
      
      // 生产环境发送到错误监控服务
      if (!__DEV__ && error) {
        Sentry.captureException(error, {
          extra: { message, args }
        });
      }
    }
  }
}

export const logger = new Logger();
```

## 性能优化

### 组件优化
```typescript
// 使用React.memo优化重渲染
export const CharacterCard = React.memo<ICharacterCardProps>(({
  character,
  onPress,
  onLongPress
}) => {
  // 组件实现
}, (prevProps, nextProps) => {
  // 自定义比较函数
  return (
    prevProps.character.id === nextProps.character.id &&
    prevProps.character.updatedAt === nextProps.character.updatedAt
  );
});

// 使用useMemo缓存计算结果
const ExpensiveComponent: React.FC<Props> = ({ data }) => {
  const processedData = useMemo(() => {
    return data.map(item => ({
      ...item,
      processed: expensiveCalculation(item)
    }));
  }, [data]);
  
  return <View>{/* 渲染处理后的数据 */}</View>;
};

// 使用useCallback缓存函数
const ParentComponent: React.FC = () => {
  const [count, setCount] = useState(0);
  
  const handlePress = useCallback(() => {
    setCount(prev => prev + 1);
  }, []);
  
  return <ChildComponent onPress={handlePress} />;
};
```

### 列表优化
```typescript
// 使用FlatList的优化配置
<FlatList
  data={characters}
  renderItem={renderCharacterCard}
  keyExtractor={(item) => item.id}
  // 性能优化配置
  removeClippedSubviews={true}
  maxToRenderPerBatch={10}
  updateCellsBatchingPeriod={50}
  initialNumToRender={10}
  windowSize={10}
  getItemLayout={(data, index) => ({
    length: ITEM_HEIGHT,
    offset: ITEM_HEIGHT * index,
    index
  })}
  // 避免不必要的重渲染
  extraData={selectedId}
/>
```

通过遵循以上开发指南，团队可以高效、规范地开发NEXTNOVEL APP，确保代码质量和项目的可维护性。

## 常见问题解决

### Metro打包问题
```bash
# 清理Metro缓存
npx react-native start --reset-cache

# 清理node_modules
rm -rf node_modules && npm install

# 清理Expo缓存
expo r -c
```

### iOS构建问题
```bash
# 清理iOS构建缓存
cd ios && rm -rf build && cd ..

# 重新安装CocoaPods
cd ios && pod deintegrate && pod install && cd ..

# 清理Xcode DerivedData
rm -rf ~/Library/Developer/Xcode/DerivedData
```

### Android构建问题
```bash
# 清理Android构建缓存
cd android && ./gradlew clean && cd ..

# 重置Android项目
cd android && rm -rf .gradle build && cd ..
```

### 性能调试
```typescript
// 使用Flipper进行性能分析
import { logger } from '@/utils/logger';

const performanceMonitor = {
  startTiming: (label: string) => {
    console.time(label);
    logger.debug(`Performance: Started timing ${label}`);
  },

  endTiming: (label: string) => {
    console.timeEnd(label);
    logger.debug(`Performance: Ended timing ${label}`);
  },

  measureRender: (componentName: string) => {
    return (WrappedComponent: React.ComponentType<any>) => {
      return React.forwardRef((props, ref) => {
        const renderStart = performance.now();

        useEffect(() => {
          const renderEnd = performance.now();
          logger.debug(`${componentName} render time: ${renderEnd - renderStart}ms`);
        });

        return <WrappedComponent {...props} ref={ref} />;
      });
    };
  }
};
```
